[package]
name = "ttx-integration"
version = "0.1.0"
edition = "2024"

[dependencies]
diesel ={ version = "=2.2", features = ["sqlite", "returning_clauses_for_sqlite_3_35"] }
serenity = { version = "=0.12" }
rocket = { version = "=0.5", features = ["json"] }
tokio = { version = "=1.45", features = ["macros", "rt-multi-thread"] }
reqwest = { version = "=0.12", features = ["json"] }
serde = { version = "=1.0", features = ["derive"] }
serde_json = { version = "=1.0" } 
once_cell = { version = "=1.21" } 
thiserror = { version = "=2.0" }
dotenv = { version = "=0.15" }
regex = { version = "=1.11" }
tracing = { version = "=0.1"} 
tracing-subscriber = { version = "=0.3" } 