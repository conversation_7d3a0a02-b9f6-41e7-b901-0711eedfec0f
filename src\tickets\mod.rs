// TODO: try fixing other issues such as sending messages and responding to interactions correctly.

mod create_ticket_channel;
pub mod models;
pub mod schema;

pub use create_ticket_channel::*;

use once_cell::sync::Lazy;
use std::collections::HashMap;
use std::sync::RwLock;

use models::{Ticket, TicketChangeset};

use crate::{models::Snowflake, tickets::models::NewTicket};

pub struct Tickets {
    tickets: RwLock<HashMap<Snowflake, Ticket>>,
}

impl Tickets {
    pub fn new() -> Self {
        Self {
            tickets: RwLock::new(HashMap::new()),
        }
    }

    /// fetches a ticket by user id snowflake.
    pub fn fetch(&self, id: Snowflake) -> Option<Ticket> {
        if let Ok(tickets) = self.tickets.read() {
            tickets.get(&id).cloned()
        } else {
            None
        }
    }

    pub fn insert(&self, ticket: NewTicket) {
        if let Ok(mut tickets) = self.tickets.write() {
            tickets.insert(
                ticket.user_id,
                Ticket {
                    id: 0,
                    user_id: ticket.user_id,
                    guild_id: ticket.guild_id,
                    category_id: ticket.category_id,
                    channel_id: ticket.channel_id,
                    contact_id: ticket.contact_id,
                    contact_name: ticket.contact_name,
                },
            );
        }
    }

    pub fn update(&self, id: Snowflake, update: TicketChangeset) -> Option<Ticket> {
        if let Ok(mut tickets) = self.tickets.write() {
            if let Some(ticket) = tickets.get_mut(&id) {
                if let Some(guild_id) = update.guild_id {
                    ticket.guild_id = guild_id;
                }

                if let Some(category_id) = update.category_id {
                    ticket.category_id = category_id;
                }

                if let Some(channel_id) = update.channel_id {
                    ticket.channel_id = channel_id;
                }

                if let Some(contact_name) = update.contact_name {
                    ticket.contact_name = contact_name;
                }

                return Some(ticket.clone());
            }
        }

        None
    }
}

pub static TICKETS: Lazy<Tickets> = Lazy::new(Tickets::new);
