use diesel::{prelude::*, sql_types::Text};

use crate::models::Snowflake;

impl Expression for Snowflake {
    type SqlType = Text;
}

impl AppearsOnTable<super::schema::tickets::table> for Snowflake {}

#[derive(Debug, Queryable, Selectable, Clone, Default)]
#[diesel(table_name = super::schema::tickets)]
pub struct Ticket {
    #[allow(dead_code)]
    pub id: i32,
    pub user_id: Snowflake,
    pub guild_id: Snowflake,
    pub category_id: Snowflake,
    pub channel_id: Snowflake,
    pub contact_id: String,
    pub contact_name: String,
}

#[derive(Debug, Insertable, Clone, Default)]
#[diesel(table_name = super::schema::tickets)]
pub struct NewTicket {
    pub user_id: Snowflake,
    pub guild_id: Snowflake,
    pub category_id: Snowflake,
    pub channel_id: Snowflake,
    pub contact_id: String,
    pub contact_name: String,
}

#[derive(Debug, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Default)]
#[diesel(table_name = super::schema::tickets)]
pub struct TicketChangeset {
    pub guild_id: Option<Snowflake>,
    pub category_id: Option<Snowflake>,
    pub channel_id: Option<Snowflake>,
    pub contact_name: Option<String>,
}
